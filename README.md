# VocalHire.ai - Flask Web Application

A comprehensive Flask web application for AI-powered recruitment with candidate management, automated interviews, and billing integration.

## Features

### Authentication
- ✅ **Login Page** - Centered card with purple theme, email/password fields, remember me checkbox, forgot password link
- ✅ **Signup Page** - Account creation with full name, company name, email, password confirmation, terms agreement
- ✅ **Responsive Design** - Mobile-friendly authentication pages

### Dashboard (Recruiter View)
- ✅ **Overview Dashboard** - Statistics cards, recent candidates table, navigation tabs
- ✅ **Candidate Management** - View all candidates, track status, manage interviews
- ✅ **CSV Upload** - Bulk candidate upload with preview functionality
- ✅ **Manual Entry** - Individual candidate addition form
- 🚧 **Email Management** - Magic link generation and email campaigns (coming soon)
- 🚧 **Prompt Customization** - AI interview prompt management (coming soon)
- 🚧 **Billing & Subscription** - Stripe integration for payments (coming soon)
- 🚧 **Profile & Settings** - Account management (coming soon)
- 🚧 **Support System** - Help desk and FAQ (coming soon)

## Installation & Setup

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation Steps

1. **Clone or download the project files**
   ```bash
   # Navigate to the project directory
   cd va
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```
   
   Or if using Python 3 specifically:
   ```bash
   python3 -m pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```
   
   Or:
   ```bash
   python3 app.py
   ```

4. **Access the application**
   - Open your web browser
   - Navigate to: `http://localhost:5000`
   - You'll be redirected to the login page

## Usage

### Getting Started
1. **Create an Account**
   - Click "Sign up" on the login page
   - Fill in your details (full name, company name, email, password)
   - Agree to terms and create your account

2. **Dashboard Overview**
   - View your monthly interview statistics
   - Check remaining credits
   - See your current subscription plan
   - Review recent candidates

3. **Add Candidates**
   - **CSV Upload**: Upload a CSV file with columns: email, name, role, notes
   - **Manual Entry**: Add individual candidates through the form

4. **Manage Candidates**
   - View all candidates in the candidates tab
   - Track invite status (pending, sent, opened, expired)
   - Monitor interview status (not started, in progress, completed)
   - View AI scores and summaries

## File Structure

```
va/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── templates/
│   ├── base.html         # Base template
│   ├── auth/
│   │   ├── login.html    # Login page
│   │   └── signup.html   # Signup page
│   └── dashboard/
│       └── overview.html # Dashboard template
├── static/
│   ├── css/
│   │   └── style.css     # Main stylesheet
│   ├── js/
│   │   └── main.js       # JavaScript functionality
│   └── images/           # Image assets
└── uploads/              # File upload directory
```

## Database Schema

The application uses SQLite with the following models:

- **User**: User accounts with authentication and subscription info
- **Candidate**: Candidate information and interview status
- **InterviewPrompt**: Customizable AI interview prompts

## Styling & Design

The application follows the VocalHire.ai design specifications:

- **Color Scheme**: Purple primary (#6b46c1), light gray backgrounds (#f7fafc)
- **Typography**: System fonts with proper hierarchy
- **Layout**: Centered cards for auth, dashboard with navigation tabs
- **Responsive**: Mobile-first design with proper breakpoints
- **Components**: Consistent buttons, forms, tables, and status badges

## Development Notes

### Current Implementation Status
- ✅ Complete: Authentication system, basic dashboard, candidate management
- 🚧 In Progress: Email system, billing integration, advanced features
- 📋 Planned: Analytics, reporting, support system

### Next Steps
1. Implement email system with magic links
2. Add Stripe billing integration
3. Build prompt customization features
4. Create support and help system
5. Add analytics and reporting

## Troubleshooting

### Common Issues

1. **Python not found**
   - Install Python from python.org
   - Make sure Python is added to your system PATH

2. **Module not found errors**
   - Run `pip install -r requirements.txt` to install dependencies
   - Use virtual environment for better dependency management

3. **Database errors**
   - Delete `vocalhire.db` file and restart the application
   - The database will be recreated automatically

4. **Port already in use**
   - Change the port in app.py: `app.run(debug=True, port=5001)`

## Contributing

This is a demonstration project showcasing Flask web development with modern UI/UX design principles and comprehensive feature planning.

## License

This project is for demonstration purposes.
