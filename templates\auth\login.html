{% extends "base.html" %}

{% block title %}Sign in - VocalHire.ai{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-logo">
            <h1>VocalHire.ai</h1>
        </div>
        
        <h2 class="auth-subtitle">Sign in to your account</h2>
        
        <form method="POST" id="login-form">
            <div class="form-group">
                <label for="email" class="form-label">Email address</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    class="form-input" 
                    required 
                    placeholder="Enter your email"
                >
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <div class="password-input-container">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        required 
                        placeholder="Enter your password"
                    >
                    <button 
                        type="button" 
                        class="password-toggle" 
                        onclick="togglePassword('password')"
                        aria-label="Toggle password visibility"
                    >
                        👁️
                    </button>
                </div>
            </div>
            
            <div class="form-row">
                <div class="checkbox-container">
                    <input type="checkbox" id="remember" name="remember" value="1">
                    <label for="remember" class="checkbox-label">Remember me</label>
                </div>
                
                <a href="#" class="forgot-link">Forgot your password?</a>
            </div>
            
            <button type="submit" class="btn-primary">Sign in</button>
        </form>
        
        <div class="auth-link">
            Don't have an account? <a href="{{ url_for('signup') }}">Sign up</a>
        </div>
    </div>
</div>
{% endblock %}
