from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
from datetime import datetime, timedelta
import jwt
import secrets
import csv
import io
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib

app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///vocalhire.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Database Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(100), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    company_name = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    subscription_plan = db.Column(db.String(20), default='starter')
    interviews_this_month = db.Column(db.Integer, default=0)
    remaining_credits = db.Column(db.Integer, default=10)
    
class Candidate(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(100))
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    magic_token = db.Column(db.String(200))
    invite_status = db.Column(db.String(20), default='pending')  # sent, opened, expired
    interview_status = db.Column(db.String(20), default='not_started')  # in_progress, completed
    ai_score = db.Column(db.Float)
    ai_summary = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    invited_at = db.Column(db.DateTime)

class InterviewPrompt(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(100))
    prompt_text = db.Column(db.Text, nullable=False)
    is_default = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        remember = bool(request.form.get('remember'))
        
        user = User.query.filter_by(email=email).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user, remember=remember)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid email or password', 'error')
    
    return render_template('auth/login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        full_name = request.form.get('full_name')
        company_name = request.form.get('company_name')
        
        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('auth/signup.html')
        
        if User.query.filter_by(email=email).first():
            flash('Email already exists', 'error')
            return render_template('auth/signup.html')
        
        user = User(
            email=email,
            password_hash=generate_password_hash(password),
            full_name=full_name,
            company_name=company_name
        )
        
        db.session.add(user)
        db.session.commit()
        
        login_user(user)
        return redirect(url_for('dashboard'))
    
    return render_template('auth/signup.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    candidates = Candidate.query.filter_by(user_id=current_user.id).all()
    return render_template('dashboard/overview.html', candidates=candidates)

@app.route('/add-candidate', methods=['POST'])
@login_required
def add_candidate():
    name = request.form.get('name')
    email = request.form.get('email')
    role = request.form.get('role')
    notes = request.form.get('notes')

    # Check if candidate already exists
    existing = Candidate.query.filter_by(email=email, user_id=current_user.id).first()
    if existing:
        flash('Candidate with this email already exists', 'error')
        return redirect(url_for('dashboard'))

    candidate = Candidate(
        name=name,
        email=email,
        role=role,
        notes=notes,
        user_id=current_user.id,
        magic_token=secrets.token_urlsafe(32)
    )

    db.session.add(candidate)
    db.session.commit()

    flash('Candidate added successfully', 'success')
    return redirect(url_for('dashboard'))

@app.route('/upload-csv', methods=['POST'])
@login_required
def upload_csv():
    if 'csv_file' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('dashboard'))

    file = request.files['csv_file']
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('dashboard'))

    if file and file.filename.endswith('.csv'):
        try:
            # Read CSV content
            stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
            csv_input = csv.reader(stream)

            # Skip header row
            headers = next(csv_input)

            candidates_added = 0
            for row in csv_input:
                if len(row) >= 2:  # At least email and name
                    email = row[0].strip()
                    name = row[1].strip()
                    role = row[2].strip() if len(row) > 2 else ''
                    notes = row[3].strip() if len(row) > 3 else ''

                    # Check if candidate already exists
                    existing = Candidate.query.filter_by(email=email, user_id=current_user.id).first()
                    if not existing and email and name:
                        candidate = Candidate(
                            name=name,
                            email=email,
                            role=role,
                            notes=notes,
                            user_id=current_user.id,
                            magic_token=secrets.token_urlsafe(32)
                        )
                        db.session.add(candidate)
                        candidates_added += 1

            db.session.commit()
            flash(f'Successfully added {candidates_added} candidates', 'success')

        except Exception as e:
            flash(f'Error processing CSV file: {str(e)}', 'error')
    else:
        flash('Please upload a valid CSV file', 'error')

    return redirect(url_for('dashboard'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
