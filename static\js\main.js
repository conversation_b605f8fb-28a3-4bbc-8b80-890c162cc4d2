// Main JavaScript functionality

// Password toggle functionality
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.nextElementSibling;
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.innerHTML = '👁️‍🗨️';
    } else {
        input.type = 'password';
        toggle.innerHTML = '👁️';
    }
}

// Auto-hide flash messages
document.addEventListener('DOMContentLoaded', function() {
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(function(message) {
        setTimeout(function() {
            message.style.opacity = '0';
            setTimeout(function() {
                message.remove();
            }, 300);
        }, 5000);
    });
});

// Dashboard tab navigation
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tabs
    const tabs = document.querySelectorAll('.nav-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected tab content
    const selectedContent = document.getElementById(tabName + '-tab');
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
    }
    
    // Add active class to selected tab
    const selectedTab = document.querySelector(`[onclick="showTab('${tabName}')"]`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
}

// File upload preview
function previewCSV(input) {
    const file = input.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const csv = e.target.result;
            const lines = csv.split('\n');
            const preview = document.getElementById('csv-preview');
            
            if (lines.length > 0) {
                let table = '<table class="table"><thead><tr>';
                const headers = lines[0].split(',');
                headers.forEach(header => {
                    table += `<th>${header.trim()}</th>`;
                });
                table += '</tr></thead><tbody>';
                
                // Show first 5 rows as preview
                for (let i = 1; i < Math.min(6, lines.length); i++) {
                    if (lines[i].trim()) {
                        table += '<tr>';
                        const cells = lines[i].split(',');
                        cells.forEach(cell => {
                            table += `<td>${cell.trim()}</td>`;
                        });
                        table += '</tr>';
                    }
                }
                
                table += '</tbody></table>';
                preview.innerHTML = table;
                preview.classList.remove('hidden');
            }
        };
        reader.readAsText(file);
    }
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
        } else {
            input.classList.remove('error');
        }
    });
    
    return isValid;
}

// Confirm actions
function confirmAction(message) {
    return confirm(message);
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('Copied to clipboard!', 'success');
    });
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `flash-message flash-${type}`;
    notification.innerHTML = `
        ${message}
        <button class="flash-close" onclick="this.parentElement.remove()">&times;</button>
    `;
    
    const container = document.querySelector('.flash-messages') || createFlashContainer();
    container.appendChild(notification);
    
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function createFlashContainer() {
    const container = document.createElement('div');
    container.className = 'flash-messages';
    document.body.appendChild(container);
    return container;
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Show first tab by default
    const firstTab = document.querySelector('.nav-tab');
    if (firstTab) {
        const tabName = firstTab.getAttribute('onclick').match(/'([^']+)'/)[1];
        showTab(tabName);
    }
    
    // Add form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const formId = form.getAttribute('id');
            if (formId && !validateForm(formId)) {
                e.preventDefault();
                showNotification('Please fill in all required fields', 'error');
            }
        });
    });
});
