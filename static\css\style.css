/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Flash messages */
.flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.flash-message {
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.flash-error {
    background-color: #fee;
    color: #c53030;
    border-left: 4px solid #c53030;
}

.flash-success {
    background-color: #f0fff4;
    color: #38a169;
    border-left: 4px solid #38a169;
}

.flash-close {
    position: absolute;
    top: 8px;
    right: 12px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.flash-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Auth pages styling */
.auth-container {
    min-height: 100vh;
    background-color: #f7fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
}

.auth-logo {
    text-align: center;
    margin-bottom: 8px;
}

.auth-logo h1 {
    font-size: 28px;
    font-weight: 700;
    color: #6b46c1;
    margin: 0;
}

.auth-subtitle {
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 32px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #6b46c1;
    box-shadow: 0 0 0 3px rgba(107, 70, 193, 0.1);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    font-size: 18px;
    padding: 4px;
}

.password-toggle:hover {
    color: #374151;
}

.form-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.checkbox-container {
    display: flex;
    align-items: center;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
}

.checkbox-label {
    font-size: 14px;
    color: #374151;
}

.forgot-link {
    font-size: 14px;
    color: #6b46c1;
    text-decoration: none;
}

.forgot-link:hover {
    text-decoration: underline;
}

.btn-primary {
    width: 100%;
    background-color: #6b46c1;
    color: white;
    border: none;
    padding: 14px 16px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
}

.btn-primary:hover {
    background-color: #553c9a;
    transform: translateY(-1px);
}

.btn-primary:active {
    transform: translateY(0);
}

.auth-link {
    text-align: center;
    margin-top: 24px;
    font-size: 14px;
    color: #6b7280;
}

.auth-link a {
    color: #6b46c1;
    text-decoration: none;
    font-weight: 500;
}

.auth-link a:hover {
    text-decoration: underline;
}

/* Responsive design */
@media (max-width: 480px) {
    .auth-card {
        padding: 24px;
        margin: 10px;
    }
    
    .auth-logo h1 {
        font-size: 24px;
    }
    
    .auth-subtitle {
        font-size: 18px;
    }
    
    .form-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .forgot-link {
        align-self: flex-end;
    }
}

/* Dashboard styles */
.dashboard-container {
    min-height: 100vh;
    background-color: #f8fafc;
}

.dashboard-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-logo {
    font-size: 24px;
    font-weight: 700;
    color: #6b46c1;
}

.dashboard-user {
    display: flex;
    align-items: center;
    gap: 16px;
}

.dashboard-nav {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 0 24px;
}

.nav-tabs {
    display: flex;
    gap: 32px;
    list-style: none;
}

.nav-tab {
    padding: 16px 0;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: #64748b;
    transition: color 0.2s, border-color 0.2s;
}

.nav-tab:hover,
.nav-tab.active {
    color: #6b46c1;
    border-bottom-color: #6b46c1;
}

.dashboard-content {
    padding: 24px;
}

.content-header {
    margin-bottom: 24px;
}

.content-title {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 8px;
}

.content-subtitle {
    color: #64748b;
    font-size: 16px;
}

/* Cards */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 24px;
    margin-bottom: 24px;
}

.card-header {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.card-title {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
}

/* Tables */
.table-container {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.table th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.table td {
    color: #4b5563;
}

.table tbody tr:hover {
    background-color: #f8fafc;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-sent {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-completed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-expired {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-secondary {
    background-color: #f1f5f9;
    color: #475569;
}

.btn-secondary:hover {
    background-color: #e2e8f0;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-4 {
    margin-bottom: 16px;
}

.mt-4 {
    margin-top: 16px;
}

.hidden {
    display: none;
}

.flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.items-center {
    align-items: center;
}

.gap-4 {
    gap: 16px;
}

.gap-6 {
    gap: 24px;
}

/* Grid system */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

@media (min-width: 768px) {
    .md\\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .lg\\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .lg\\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

/* Text utilities */
.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.text-purple-600 {
    color: #7c3aed;
}

.text-green-600 {
    color: #059669;
}

.text-gray-500 {
    color: #6b7280;
}

.capitalize {
    text-transform: capitalize;
}

/* Spacing utilities */
.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

/* Form improvements */
textarea.form-input {
    resize: vertical;
    min-height: 80px;
}

small {
    font-size: 0.875rem;
    color: #6b7280;
}

/* File input styling */
input[type="file"].form-input {
    padding: 8px 12px;
    background-color: #f9fafb;
}

input[type="file"].form-input::-webkit-file-upload-button {
    background-color: #6b46c1;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    margin-right: 12px;
    cursor: pointer;
}

input[type="file"].form-input::-webkit-file-upload-button:hover {
    background-color: #553c9a;
}
