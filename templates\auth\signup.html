{% extends "base.html" %}

{% block title %}Create account - VocalHire.ai{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-logo">
            <h1>VocalHire.ai</h1>
        </div>
        
        <h2 class="auth-subtitle">Create your account</h2>
        
        <form method="POST" id="signup-form">
            <div class="form-group">
                <label for="full_name" class="form-label">Full Name</label>
                <input 
                    type="text" 
                    id="full_name" 
                    name="full_name" 
                    class="form-input" 
                    required 
                    placeholder="Enter your full name"
                >
            </div>
            
            <div class="form-group">
                <label for="company_name" class="form-label">Company Name (Optional)</label>
                <input 
                    type="text" 
                    id="company_name" 
                    name="company_name" 
                    class="form-input" 
                    placeholder="Enter your company name"
                >
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">Email address</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    class="form-input" 
                    required 
                    placeholder="Enter your email"
                >
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <div class="password-input-container">
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        required 
                        placeholder="Create a password"
                        minlength="6"
                    >
                    <button 
                        type="button" 
                        class="password-toggle" 
                        onclick="togglePassword('password')"
                        aria-label="Toggle password visibility"
                    >
                        👁️
                    </button>
                </div>
            </div>
            
            <div class="form-group">
                <label for="confirm_password" class="form-label">Confirm Password</label>
                <div class="password-input-container">
                    <input 
                        type="password" 
                        id="confirm_password" 
                        name="confirm_password" 
                        class="form-input" 
                        required 
                        placeholder="Confirm your password"
                        minlength="6"
                    >
                    <button 
                        type="button" 
                        class="password-toggle" 
                        onclick="togglePassword('confirm_password')"
                        aria-label="Toggle password visibility"
                    >
                        👁️
                    </button>
                </div>
            </div>
            
            <div class="form-group">
                <div class="checkbox-container">
                    <input type="checkbox" id="terms" name="terms" required>
                    <label for="terms" class="checkbox-label">
                        I agree to the <a href="#" class="forgot-link">Terms & Privacy Policy</a>
                    </label>
                </div>
            </div>
            
            <button type="submit" class="btn-primary">Sign Up</button>
        </form>
        
        <div class="auth-link">
            Already have an account? <a href="{{ url_for('login') }}">Sign in</a>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('password').addEventListener('input', function() {
    const confirmPassword = document.getElementById('confirm_password');
    if (confirmPassword.value) {
        confirmPassword.dispatchEvent(new Event('input'));
    }
});
</script>
{% endblock %}
