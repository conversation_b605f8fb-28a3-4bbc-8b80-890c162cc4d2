{% extends "base.html" %}

{% block title %}Dashboard - VocalHire.ai{% endblock %}

{% block content %}
<div class="dashboard-container">
    <header class="dashboard-header">
        <div class="dashboard-logo">VocalHire.ai</div>
        <div class="dashboard-user">
            <span>Welcome, {{ current_user.full_name }}</span>
            <a href="{{ url_for('logout') }}" class="btn btn-secondary btn-sm">Logout</a>
        </div>
    </header>
    
    <nav class="dashboard-nav">
        <ul class="nav-tabs">
            <li class="nav-tab active" onclick="showTab('overview')">Overview</li>
            <li class="nav-tab" onclick="showTab('candidates')">Candidates</li>
            <li class="nav-tab" onclick="showTab('upload')">Upload</li>
            <li class="nav-tab" onclick="showTab('emails')">Emails</li>
            <li class="nav-tab" onclick="showTab('prompts')">Prompts</li>
            <li class="nav-tab" onclick="showTab('billing')">Billing</li>
            <li class="nav-tab" onclick="showTab('profile')">Profile</li>
            <li class="nav-tab" onclick="showTab('support')">Support</li>
        </ul>
    </nav>
    
    <main class="dashboard-content">
        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content">
            <div class="content-header">
                <h1 class="content-title">Dashboard Overview</h1>
                <p class="content-subtitle">Welcome to your VocalHire.ai dashboard</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">This Month</h3>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600">{{ current_user.interviews_this_month }}</div>
                        <div class="text-gray-500">Interviews Completed</div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Credits Remaining</h3>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">{{ current_user.remaining_credits }}</div>
                        <div class="text-gray-500">Available Credits</div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Subscription</h3>
                    </div>
                    <div class="text-center">
                        <div class="text-xl font-semibold capitalize">{{ current_user.subscription_plan }}</div>
                        <div class="text-gray-500">Current Plan</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Candidates</h3>
                </div>
                {% if candidates %}
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Interview</th>
                                <th>Score</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for candidate in candidates[:5] %}
                            <tr>
                                <td>{{ candidate.name }}</td>
                                <td>{{ candidate.role or 'N/A' }}</td>
                                <td>
                                    <span class="status-badge status-{{ candidate.invite_status }}">
                                        {{ candidate.invite_status }}
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge status-{{ candidate.interview_status.replace('_', '-') }}">
                                        {{ candidate.interview_status.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>
                                    {% if candidate.ai_score %}
                                        {{ "%.1f"|format(candidate.ai_score) }}/10
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ candidate.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <p class="text-gray-500">No candidates yet. Start by uploading your first batch!</p>
                    <button class="btn btn-primary mt-4" onclick="showTab('upload')">Upload Candidates</button>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Candidates Tab -->
        <div id="candidates-tab" class="tab-content hidden">
            <div class="content-header">
                <h1 class="content-title">Candidate Management</h1>
                <p class="content-subtitle">Track and manage all your candidates</p>
            </div>
            
            <div class="card">
                <div class="card-header flex justify-between items-center">
                    <h3 class="card-title">All Candidates</h3>
                    <button class="btn btn-primary" onclick="showTab('upload')">Add Candidates</button>
                </div>
                
                {% if candidates %}
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Invite Status</th>
                                <th>Interview Status</th>
                                <th>AI Score</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for candidate in candidates %}
                            <tr>
                                <td>{{ candidate.name }}</td>
                                <td>{{ candidate.email }}</td>
                                <td>{{ candidate.role or 'N/A' }}</td>
                                <td>
                                    <span class="status-badge status-{{ candidate.invite_status }}">
                                        {{ candidate.invite_status }}
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge status-{{ candidate.interview_status.replace('_', '-') }}">
                                        {{ candidate.interview_status.replace('_', ' ').title() }}
                                    </span>
                                </td>
                                <td>
                                    {% if candidate.ai_score %}
                                        {{ "%.1f"|format(candidate.ai_score) }}/10
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-secondary">View</button>
                                    {% if candidate.invite_status == 'pending' %}
                                    <button class="btn btn-sm btn-primary">Send Invite</button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <p class="text-gray-500">No candidates found. Upload your first batch to get started!</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Upload Tab -->
        <div id="upload-tab" class="tab-content hidden">
            <div class="content-header">
                <h1 class="content-title">Upload Candidates</h1>
                <p class="content-subtitle">Add candidates via CSV upload or manual entry</p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">CSV Upload</h3>
                    </div>
                    
                    <form method="POST" enctype="multipart/form-data" action="/upload-csv">
                        <div class="form-group">
                            <label class="form-label">Upload CSV File</label>
                            <input 
                                type="file" 
                                name="csv_file" 
                                accept=".csv" 
                                class="form-input"
                                onchange="previewCSV(this)"
                                required
                            >
                            <small class="text-gray-500">
                                CSV should contain columns: email, name, role, notes
                            </small>
                        </div>
                        
                        <div id="csv-preview" class="hidden mt-4">
                            <h4 class="font-semibold mb-2">Preview:</h4>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Upload Candidates</button>
                    </form>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Manual Entry</h3>
                    </div>
                    
                    <form method="POST" action="/add-candidate">
                        <div class="form-group">
                            <label class="form-label">Full Name</label>
                            <input type="text" name="name" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Email Address</label>
                            <input type="email" name="email" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Role</label>
                            <input type="text" name="role" class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Notes</label>
                            <textarea name="notes" class="form-input" rows="3"></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Add Candidate</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Other tabs will be implemented in subsequent updates -->
        <div id="emails-tab" class="tab-content hidden">
            <div class="content-header">
                <h1 class="content-title">Email Management</h1>
                <p class="content-subtitle">Manage invitations and email campaigns</p>
            </div>
            <div class="card">
                <p>Email management features coming soon...</p>
            </div>
        </div>
        
        <div id="prompts-tab" class="tab-content hidden">
            <div class="content-header">
                <h1 class="content-title">Interview Prompts</h1>
                <p class="content-subtitle">Customize your AI interview prompts</p>
            </div>
            <div class="card">
                <p>Prompt customization features coming soon...</p>
            </div>
        </div>
        
        <div id="billing-tab" class="tab-content hidden">
            <div class="content-header">
                <h1 class="content-title">Billing & Subscription</h1>
                <p class="content-subtitle">Manage your subscription and billing</p>
            </div>
            <div class="card">
                <p>Billing management features coming soon...</p>
            </div>
        </div>
        
        <div id="profile-tab" class="tab-content hidden">
            <div class="content-header">
                <h1 class="content-title">Profile & Settings</h1>
                <p class="content-subtitle">Manage your account settings</p>
            </div>
            <div class="card">
                <p>Profile management features coming soon...</p>
            </div>
        </div>
        
        <div id="support-tab" class="tab-content hidden">
            <div class="content-header">
                <h1 class="content-title">Help & Support</h1>
                <p class="content-subtitle">Get help and support</p>
            </div>
            <div class="card">
                <p>Support features coming soon...</p>
            </div>
        </div>
    </main>
</div>
{% endblock %}
